-- =====================================================
-- FIX APPOINTMENTS TABLE SCHEMA ISSUE
-- =====================================================
-- Run this SQL in your Supabase dashboard SQL editor to fix the schema cache issue

-- First, let's check if the appointments table exists and has the address column
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'appointments' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- If the address column is missing, add it
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS address TEXT;

-- Ensure all required columns exist for the enhanced booking form
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS date_of_birth DATE;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS medical_history TEXT;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS allergies TEXT;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS current_medications TEXT;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS previous_dental_work TEXT;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS dental_concerns TEXT;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS emergency_contact_name VARCHAR(255);
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS emergency_contact_phone VARCHAR(20);
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS emergency_contact_relationship VARCHAR(100);
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS has_insurance VARCHAR(10) CHECK (has_insurance IN ('yes', 'no', 'unsure'));
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS insurance_provider VARCHAR(255);
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS insurance_policy_number VARCHAR(255);

-- Refresh the schema cache by running a simple query
SELECT 1 FROM appointments LIMIT 1;

-- Verify all columns are now present
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'appointments' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Success message
SELECT 'Appointments table schema updated successfully! All required columns are now present.' as message;
