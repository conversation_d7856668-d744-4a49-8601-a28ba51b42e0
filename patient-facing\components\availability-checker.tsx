"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, Clock, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { format } from 'date-fns'

interface AvailabilitySlot {
  time: string
  available: boolean
  maxAppointments?: number
  currentAppointments?: number
  capacity?: number
  total?: number
}

interface AvailabilityCheckerProps {
  selectedDate: string
  onTimeSelect: (time: string) => void
  selectedTime?: string
}

export function AvailabilityChecker({ selectedDate, onTimeSelect, selectedTime }: AvailabilityCheckerProps) {
  const [availableSlots, setAvailableSlots] = useState<AvailabilitySlot[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (selectedDate) {
      fetchAvailability()
    }
  }, [selectedDate])

  const fetchAvailability = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/availability?date=${selectedDate}`)
      const data = await response.json()
      
      if (data.available && data.timeSlots) {
        // Filter out fully booked slots and format the data
        const availableTimeSlots = data.timeSlots
          .filter((slot: any) => {
            const currentBookings = slot.current_appointments || 0
            return currentBookings < slot.max_appointments
          })
          .map((slot: any) => ({
            time: slot.time,
            available: true,
            capacity: slot.max_appointments - (slot.current_appointments || 0),
            total: slot.max_appointments
          }))

        setAvailableSlots(availableTimeSlots)
      } else {
        setAvailableSlots([])
        setError(data.message || 'No availability for this date')
      }
    } catch (err) {
      setError('Failed to check availability')
      console.error('Error fetching availability:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleTimeSelect = (time: string) => {
    onTimeSelect(time)
  }

  if (!selectedDate) {
    return (
      <div className="text-center py-8">
        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">Please select a date to view available time slots</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
        <p className="text-muted-foreground">Checking availability...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchAvailability} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    )
  }

  if (availableSlots.length === 0) {
    return (
      <div className="text-center py-8">
        <XCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
        <p className="text-orange-600 mb-2">No available slots for {format(new Date(selectedDate), 'MMMM d, yyyy')}</p>
        <p className="text-muted-foreground text-sm">Please select a different date or contact us directly</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center gap-2 mb-4">
        <Calendar className="h-5 w-5 text-primary" />
        <span className="font-medium text-foreground">
          Available slots for {format(new Date(selectedDate), 'MMMM d, yyyy')}
        </span>
      </div>
      
      <div className="grid grid-cols-3 gap-3">
        {availableSlots.map((slot) => (
          <Button
            key={slot.time}
            variant={selectedTime === slot.time ? "default" : "outline"}
            className={`p-3 h-auto flex flex-col items-center transition-all duration-300 ${
              selectedTime === slot.time
                ? 'bg-primary text-primary-foreground shadow-lg'
                : 'hover:bg-primary/10'
            }`}
            onClick={() => handleTimeSelect(slot.time)}
          >
            <div className="flex items-center gap-1 mb-1">
              <Clock className="h-3 w-3" />
              <span className="text-sm font-medium">{slot.time}</span>
            </div>
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span className="text-xs">
                {slot.capacity || (slot.maxAppointments && slot.currentAppointments ?
                  slot.maxAppointments - slot.currentAppointments : 'Available')}
                {slot.capacity ? ' left' : ''}
              </span>
            </div>
          </Button>
        ))}
      </div>
      
      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
        <p className="text-sm text-muted-foreground">
          <CheckCircle className="h-4 w-4 inline text-green-500 mr-1" />
          All times shown are available for booking. Slots are updated in real-time.
        </p>
      </div>
    </div>
  )
}
