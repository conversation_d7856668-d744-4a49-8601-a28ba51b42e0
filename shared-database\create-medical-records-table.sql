-- =====================================================
-- MEDICAL RECORDS TABLE SETUP
-- =====================================================
-- Run this SQL in your Supabase dashboard SQL editor

-- Step 1: Create exec_sql function (for future database operations)
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS text AS $$
BEGIN
  EXECUTE sql;
  RETURN 'SQL executed successfully';
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'Error: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 2: Create medical_records table
CREATE TABLE IF NOT EXISTS medical_records (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  patient_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
  record_type VARCHAR(50) NOT NULL CHECK (record_type IN ('consultation', 'treatment', 'prescription', 'lab_result', 'x_ray', 'diagnosis', 'follow_up')),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  diagnosis TEXT,
  treatment TEXT,
  medications JSONB, -- Array of medication objects
  vital_signs JSONB, -- Blood pressure, temperature, etc.
  notes TEXT,
  attachments JSONB, -- Array of file URLs
  created_by VARCHAR(255), -- Doctor/staff name
  is_confidential BOOLEAN DEFAULT FALSE,
  tags TEXT[] -- For categorization
);

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_id ON medical_records(patient_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_appointment_id ON medical_records(appointment_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_record_type ON medical_records(record_type);
CREATE INDEX IF NOT EXISTS idx_medical_records_created_at ON medical_records(created_at);

-- Step 4: Enable RLS (Row Level Security)
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;

-- Step 5: Create RLS policies
CREATE POLICY "Enable read access for all users" ON medical_records FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON medical_records FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users only" ON medical_records FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users only" ON medical_records FOR DELETE USING (true);

-- Step 6: Insert sample medical records data
INSERT INTO medical_records (patient_id, record_type, title, description, diagnosis, treatment, medications, vital_signs, notes, created_by, tags) VALUES
(
  (SELECT id FROM user_profiles LIMIT 1),
  'consultation',
  'Initial Dental Consultation',
  'Comprehensive oral examination and assessment',
  'Mild gingivitis, one cavity in upper left molar',
  'Professional cleaning recommended, filling required',
  '[{"name": "Fluoride rinse", "dosage": "Daily", "duration": "2 weeks"}]'::jsonb,
  '{"blood_pressure": "120/80", "temperature": "98.6°F"}'::jsonb,
  'Patient reports sensitivity to cold. Good oral hygiene habits.',
  'Dr. Smith',
  ARRAY['routine', 'consultation']
),
(
  (SELECT id FROM user_profiles LIMIT 1),
  'treatment',
  'Dental Filling - Upper Left Molar',
  'Composite filling procedure for cavity',
  'Dental caries - upper left first molar',
  'Composite resin filling completed successfully',
  '[]'::jsonb,
  '{}'::jsonb,
  'Procedure completed without complications. Patient tolerated well.',
  'Dr. Smith',
  ARRAY['treatment', 'filling']
),
(
  (SELECT id FROM user_profiles LIMIT 1),
  'prescription',
  'Post-Treatment Medication',
  'Pain management following dental procedure',
  NULL,
  NULL,
  '[{"name": "Ibuprofen", "dosage": "400mg", "frequency": "Every 6 hours", "duration": "3 days"}]'::jsonb,
  '{}'::jsonb,
  'Take with food to avoid stomach upset. Contact office if pain persists.',
  'Dr. Smith',
  ARRAY['prescription', 'pain-management']
),
(
  (SELECT id FROM user_profiles LIMIT 1),
  'x_ray',
  'Dental X-Ray - Full Mouth',
  'Comprehensive dental radiographic examination',
  'No significant abnormalities detected',
  'Routine examination completed',
  '[]'::jsonb,
  '{}'::jsonb,
  'Clear images obtained. All teeth and supporting structures appear healthy.',
  'Dr. Smith',
  ARRAY['x-ray', 'diagnostic']
),
(
  (SELECT id FROM user_profiles LIMIT 1),
  'follow_up',
  'Post-Treatment Follow-up',
  'Follow-up examination after dental filling',
  'Healing progressing well',
  'No additional treatment required at this time',
  '[]'::jsonb,
  '{}'::jsonb,
  'Patient reports no pain or sensitivity. Filling is intact and functioning well.',
  'Dr. Smith',
  ARRAY['follow-up', 'routine']
)
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'Medical records table created successfully! You can now use the medical records API.' as message;
