{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/supabase-server.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { type Database } from './supabase'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Admin client with service role key (server-side only)\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  }\n})\n\n// Server-side client for SSR\nexport const createServerSupabaseClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          cookieStore.set({ name, value, ...options })\n        },\n        remove(name: string, options: any) {\n          cookieStore.set({ name, value: '', ...options })\n        },\n      },\n    }\n  )\n}\n\n// Type aliases for easier use\nexport type Patient = Database['public']['Tables']['user_profiles']['Row']\nexport type Appointment = Database['public']['Tables']['appointments']['Row']\nexport type Service = Database['public']['Tables']['services']['Row']\nexport type ContactForm = Database['public']['Tables']['contact_forms']['Row']\nexport type Testimonial = Database['public']['Tables']['testimonials']['Row']\n\n// API functions for admin dashboard\nexport const api = {\n  // Patients\n  async getPatients() {\n    // Get all user profiles\n    const { data: profiles, error: profilesError } = await supabaseAdmin\n      .from('user_profiles')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (profilesError) throw profilesError\n\n    // For each profile, get their appointment data and counts\n    const patientsWithAppointmentData = await Promise.all(\n      profiles.map(async (profile) => {\n        // Get all appointments for this patient\n        const { data: appointments, error: appointmentsError } = await supabaseAdmin\n          .from('appointments')\n          .select('*')\n          .eq('user_id', profile.id)\n          .order('created_at', { ascending: false })\n\n        if (appointmentsError) {\n          console.error('Error fetching appointments for patient:', profile.id, appointmentsError)\n        }\n\n        const patientAppointments = appointments || []\n\n        // Get the latest appointment for medical info\n        const latestAppointment = patientAppointments[0]\n\n        // Calculate appointment statistics\n        const totalAppointments = patientAppointments.length\n        const completedAppointments = patientAppointments.filter(apt => apt.status === 'completed').length\n        const pendingAppointments = patientAppointments.filter(apt => apt.status === 'pending').length\n        const confirmedAppointments = patientAppointments.filter(apt => apt.status === 'confirmed').length\n        const cancelledAppointments = patientAppointments.filter(apt => apt.status === 'cancelled').length\n\n        // Find the most recent completed appointment for last visit date\n        const lastCompletedAppointment = patientAppointments.find(apt => apt.status === 'completed')\n        const lastVisitDate = lastCompletedAppointment?.preferred_date || latestAppointment?.preferred_date\n\n        return {\n          ...profile,\n          // Medical information from latest appointment\n          medical_history: latestAppointment?.medical_history || profile.medical_history,\n          allergies: latestAppointment?.allergies || profile.allergies,\n          current_medications: latestAppointment?.current_medications,\n          previous_dental_work: latestAppointment?.previous_dental_work,\n          dental_concerns: latestAppointment?.dental_concerns,\n          emergency_contact: latestAppointment?.emergency_contact_name || profile.emergency_contact,\n          emergency_contact_phone: latestAppointment?.emergency_contact_phone,\n          emergency_contact_relationship: latestAppointment?.emergency_contact_relationship,\n          has_insurance: latestAppointment?.has_insurance,\n          insurance_provider: latestAppointment?.insurance_provider,\n          insurance_policy_number: latestAppointment?.insurance_policy_number,\n\n          // Appointment statistics\n          total_appointments: totalAppointments,\n          completed_appointments: completedAppointments,\n          pending_appointments: pendingAppointments,\n          confirmed_appointments: confirmedAppointments,\n          cancelled_appointments: cancelledAppointments,\n          last_visit: lastVisitDate,\n\n          // Additional patient info\n          date_of_birth: latestAppointment?.date_of_birth || profile.date_of_birth,\n          address: latestAppointment?.address || profile.address,\n          phone: latestAppointment?.phone || profile.phone\n        }\n      })\n    )\n\n    return patientsWithAppointmentData as Patient[]\n  },\n\n  async getPatient(id: string) {\n    // Get user profile\n    const { data: profile, error: profileError } = await supabaseAdmin\n      .from('user_profiles')\n      .select('*')\n      .eq('id', id)\n      .single()\n\n    if (profileError) throw profileError\n\n    // Get latest appointment with medical information\n    const { data: latestAppointment } = await supabaseAdmin\n      .from('appointments')\n      .select('*')\n      .eq('user_id', id)\n      .order('created_at', { ascending: false })\n      .limit(1)\n      .single()\n\n    // Merge profile with medical information from latest appointment\n    const patientData = {\n      ...profile,\n      medical_history: latestAppointment?.medical_history || profile.medical_history,\n      allergies: latestAppointment?.allergies || profile.allergies,\n      current_medications: latestAppointment?.current_medications || profile.current_medications,\n      previous_dental_work: latestAppointment?.previous_dental_work || profile.previous_dental_work,\n      dental_concerns: latestAppointment?.dental_concerns || profile.dental_concerns,\n      emergency_contact: latestAppointment?.emergency_contact_name || profile.emergency_contact,\n      emergency_contact_phone: latestAppointment?.emergency_contact_phone || profile.emergency_contact_phone,\n      emergency_contact_relationship: latestAppointment?.emergency_contact_relationship || profile.emergency_contact_relationship,\n      has_insurance: latestAppointment?.has_insurance || profile.has_insurance,\n      insurance_provider: latestAppointment?.insurance_provider || profile.insurance_provider,\n      insurance_policy_number: latestAppointment?.insurance_policy_number || profile.insurance_policy_number,\n      last_visit: latestAppointment?.preferred_date || profile.last_visit\n    }\n\n    return patientData as Patient\n  },\n\n  async createPatient(patient: Database['public']['Tables']['user_profiles']['Insert']) {\n    const { data, error } = await supabaseAdmin\n      .from('user_profiles')\n      .insert(patient)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Patient\n  },\n\n  async updatePatient(id: string, updates: Database['public']['Tables']['user_profiles']['Update']) {\n    const { data, error } = await supabaseAdmin\n      .from('user_profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Patient\n  },\n\n  async getPatientAppointments(patientId: string) {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .select('*')\n      .eq('user_id', patientId)\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return data\n  },\n\n  // Appointments\n  async getAppointments() {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .select('*')\n      .order('preferred_date', { ascending: true })\n\n    if (error) throw error\n    return data as Appointment[]\n  },\n\n  async getPendingAppointments() {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .select('*')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return data as Appointment[]\n  },\n\n  async getAppointment(id: string) {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .select('*')\n      .eq('id', id)\n      .single()\n\n    if (error) throw error\n    return data as Appointment\n  },\n\n  async createAppointment(appointment: any) {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .insert(appointment)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Appointment\n  },\n\n  async updateAppointment(id: string, updates: any) {\n    const { data, error } = await supabaseAdmin\n      .from('appointments')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Appointment\n  },\n\n  // Services\n  async getServices() {\n    const { data, error } = await supabaseAdmin\n      .from('services')\n      .select('*')\n      .eq('is_active', true)\n      .order('title', { ascending: true })\n    \n    if (error) throw error\n    return data as Service[]\n  },\n\n  async getAllServices() {\n    const { data, error } = await supabaseAdmin\n      .from('services')\n      .select('*')\n      .order('title', { ascending: true })\n    \n    if (error) throw error\n    return data as Service[]\n  },\n\n  async getService(id: string) {\n    const { data, error } = await supabaseAdmin\n      .from('services')\n      .select('*')\n      .eq('id', id)\n      .single()\n    \n    if (error) throw error\n    return data as Service\n  },\n\n  async createService(service: any) {\n    const { data, error } = await supabaseAdmin\n      .from('services')\n      .insert(service)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Service\n  },\n\n  async updateService(id: string, updates: any) {\n    const { data, error } = await supabaseAdmin\n      .from('services')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Service\n  },\n\n  // Contact Forms\n  async getContactForms() {\n    const { data, error } = await supabaseAdmin\n      .from('contact_forms')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as ContactForm[]\n  },\n\n  async updateContactForm(id: string, updates: any) {\n    const { data, error } = await supabaseAdmin\n      .from('contact_forms')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContactForm\n  },\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string) {\n    // Get traditional medical records\n    let medicalRecordsQuery = supabaseAdmin\n      .from('medical_records')\n      .select(`\n        *,\n        user_profiles!patient_id (\n          id,\n          full_name,\n          email,\n          phone\n        ),\n        appointments (\n          id,\n          service,\n          preferred_date,\n          preferred_time\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (patientId) {\n      medicalRecordsQuery = medicalRecordsQuery.eq('patient_id', patientId)\n    }\n\n    // Get appointments with medical information\n    let appointmentsQuery = supabaseAdmin\n      .from('appointments')\n      .select(`\n        id,\n        created_at,\n        user_id,\n        name,\n        email,\n        phone,\n        service,\n        preferred_date,\n        preferred_time,\n        status,\n        medical_history,\n        allergies,\n        current_medications,\n        previous_dental_work,\n        dental_concerns,\n        emergency_contact_name,\n        emergency_contact_phone,\n        emergency_contact_relationship,\n        has_insurance,\n        insurance_provider,\n        insurance_policy_number\n      `)\n      .not('medical_history', 'is', null)\n      .order('created_at', { ascending: false })\n\n    if (patientId) {\n      appointmentsQuery = appointmentsQuery.eq('user_id', patientId)\n    }\n\n    const [medicalRecordsResult, appointmentsResult] = await Promise.all([\n      medicalRecordsQuery,\n      appointmentsQuery\n    ])\n\n    if (medicalRecordsResult.error) throw medicalRecordsResult.error\n    if (appointmentsResult.error) throw appointmentsResult.error\n\n    // Combine and format the data\n    const medicalRecords = medicalRecordsResult.data || []\n    const appointmentRecords = (appointmentsResult.data || []).map(appointment => ({\n      id: appointment.id,\n      created_at: appointment.created_at,\n      patient_id: appointment.user_id,\n      record_type: 'appointment_intake',\n      title: `Medical Intake - ${appointment.service}`,\n      description: `Medical information collected during appointment booking for ${appointment.service}`,\n      content: {\n        service: appointment.service,\n        appointment_date: appointment.preferred_date,\n        appointment_time: appointment.preferred_time,\n        medical_history: appointment.medical_history,\n        allergies: appointment.allergies,\n        current_medications: appointment.current_medications,\n        previous_dental_work: appointment.previous_dental_work,\n        dental_concerns: appointment.dental_concerns,\n        emergency_contact: {\n          name: appointment.emergency_contact_name,\n          phone: appointment.emergency_contact_phone,\n          relationship: appointment.emergency_contact_relationship\n        },\n        insurance: {\n          has_insurance: appointment.has_insurance,\n          provider: appointment.insurance_provider,\n          policy_number: appointment.insurance_policy_number\n        }\n      },\n      user_profiles: {\n        id: appointment.user_id,\n        full_name: appointment.name,\n        email: appointment.email,\n        phone: appointment.phone\n      },\n      appointments: null\n    }))\n\n    // Combine and sort all records\n    const allRecords = [...medicalRecords, ...appointmentRecords]\n      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    return allRecords\n  },\n\n  async getMedicalRecord(id: string) {\n    const { data, error } = await supabaseAdmin\n      .from('medical_records')\n      .select(`\n        *,\n        user_profiles!patient_id (\n          id,\n          full_name,\n          email,\n          phone,\n          date_of_birth,\n          medical_history,\n          allergies\n        ),\n        appointments (\n          id,\n          service,\n          preferred_date,\n          preferred_time\n        )\n      `)\n      .eq('id', id)\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  async createMedicalRecord(record: any) {\n    const { data, error } = await supabaseAdmin\n      .from('medical_records')\n      .insert(record)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  async updateMedicalRecord(id: string, updates: any) {\n    const { data, error } = await supabaseAdmin\n      .from('medical_records')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  // Availability Slots\n  async getAvailabilitySlots() {\n    const { data, error } = await supabaseAdmin\n      .from('availability_slots')\n      .select('*')\n      .order('date', { ascending: true })\n      .order('start_time', { ascending: true })\n\n    if (error) throw error\n    return data\n  },\n\n  async createAvailabilitySlot(slot: any) {\n    const { data, error } = await supabaseAdmin\n      .from('availability_slots')\n      .insert({\n        date: slot.date,\n        start_time: slot.start_time,\n        end_time: slot.end_time,\n        is_available: slot.is_available ?? true,\n        max_appointments: slot.max_appointments ?? 1,\n        current_appointments: 0,\n        notes: slot.notes\n      })\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  async updateAvailabilitySlot(id: string, updates: any) {\n    const { data, error } = await supabaseAdmin\n      .from('availability_slots')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  async deleteAvailabilitySlot(id: string) {\n    const { error } = await supabaseAdmin\n      .from('availability_slots')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return { success: true }\n  },\n\n  async checkSlotAvailability(date: string, time?: string) {\n    // Get availability slots for the date\n    const { data: slots, error: slotsError } = await supabaseAdmin\n      .from('availability_slots')\n      .select('*')\n      .eq('date', date)\n      .eq('is_available', true)\n\n    if (slotsError) throw slotsError\n\n    // Get existing appointments for the date\n    const { data: appointments, error: appointmentsError } = await supabaseAdmin\n      .from('appointments')\n      .select('preferred_time, status')\n      .eq('preferred_date', date)\n      .in('status', ['pending', 'confirmed', 'in_progress'])\n\n    if (appointmentsError) throw appointmentsError\n\n    if (time) {\n      // Check specific time slot\n      const timeSlot = slots.find(slot => {\n        return time >= slot.start_time && time <= slot.end_time\n      })\n\n      if (!timeSlot) {\n        return {\n          available: false,\n          reason: 'No availability slot configured for this time'\n        }\n      }\n\n      const appointmentsAtTime = appointments.filter(apt => apt.preferred_time === time).length\n      const available = appointmentsAtTime < timeSlot.max_appointments\n\n      return {\n        available,\n        reason: available ? null : 'Time slot is fully booked',\n        maxAppointments: timeSlot.max_appointments,\n        currentAppointments: appointmentsAtTime\n      }\n    }\n\n    // Return all slots with availability info\n    const slotsWithAvailability = slots.map(slot => {\n      const appointmentsInSlot = appointments.filter(apt => {\n        return apt.preferred_time >= slot.start_time && apt.preferred_time <= slot.end_time\n      }).length\n\n      return {\n        ...slot,\n        current_appointments: appointmentsInSlot,\n        available: appointmentsInSlot < slot.max_appointments\n      }\n    })\n\n    return {\n      date,\n      slots: slotsWithAvailability\n    }\n  },\n\n  // Analytics\n  async getDashboardStats() {\n    const [patientsResult, appointmentsResult, servicesResult, contactFormsResult] = await Promise.all([\n      supabaseAdmin.from('user_profiles').select('id', { count: 'exact' }),\n      supabaseAdmin.from('appointments').select('id, status', { count: 'exact' }),\n      supabaseAdmin.from('services').select('id', { count: 'exact' }).eq('is_active', true),\n      supabaseAdmin.from('contact_forms').select('id', { count: 'exact' }).eq('status', 'new')\n    ])\n\n    const totalPatients = patientsResult.count || 0\n    const totalAppointments = appointmentsResult.count || 0\n    const totalServices = servicesResult.count || 0\n    const newMessages = contactFormsResult.count || 0\n\n    // Calculate appointment stats\n    const appointments = appointmentsResult.data || []\n    const pendingAppointments = appointments.filter(a => a.status === 'pending').length\n    const confirmedAppointments = appointments.filter(a => a.status === 'confirmed').length\n    const completedAppointments = appointments.filter(a => a.status === 'completed').length\n    const inProgressAppointments = appointments.filter(a => a.status === 'in_progress').length\n\n    return {\n      totalPatients,\n      totalAppointments,\n      totalServices,\n      newMessages,\n      pendingAppointments,\n      confirmedAppointments,\n      completedAppointments,\n      inProgressAppointments\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAGA,MAAM;AACN,MAAM;AACN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;AAG7D,MAAM,gBAAgB,IAAA,yMAAY,EAAC,aAAa,wBAAwB;IAC7E,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAGO,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,IAAA,4IAAO;IAEjC,OAAO,IAAA,iMAAkB,EACvB,aACA,iBACA;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AAEJ;AAUO,MAAM,MAAM;IACjB,WAAW;IACX,MAAM;QACJ,wBAAwB;QACxB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,MAAM;QAEzB,0DAA0D;QAC1D,MAAM,8BAA8B,MAAM,QAAQ,GAAG,CACnD,SAAS,GAAG,CAAC,OAAO;YAClB,wCAAwC;YACxC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,cAC5D,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QAAQ,EAAE,EACxB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,mBAAmB;gBACrB,QAAQ,KAAK,CAAC,4CAA4C,QAAQ,EAAE,EAAE;YACxE;YAEA,MAAM,sBAAsB,gBAAgB,EAAE;YAE9C,8CAA8C;YAC9C,MAAM,oBAAoB,mBAAmB,CAAC,EAAE;YAEhD,mCAAmC;YACnC,MAAM,oBAAoB,oBAAoB,MAAM;YACpD,MAAM,wBAAwB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;YAClG,MAAM,sBAAsB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;YAC9F,MAAM,wBAAwB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;YAClG,MAAM,wBAAwB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;YAElG,iEAAiE;YACjE,MAAM,2BAA2B,oBAAoB,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;YAChF,MAAM,gBAAgB,0BAA0B,kBAAkB,mBAAmB;YAErF,OAAO;gBACL,GAAG,OAAO;gBACV,8CAA8C;gBAC9C,iBAAiB,mBAAmB,mBAAmB,QAAQ,eAAe;gBAC9E,WAAW,mBAAmB,aAAa,QAAQ,SAAS;gBAC5D,qBAAqB,mBAAmB;gBACxC,sBAAsB,mBAAmB;gBACzC,iBAAiB,mBAAmB;gBACpC,mBAAmB,mBAAmB,0BAA0B,QAAQ,iBAAiB;gBACzF,yBAAyB,mBAAmB;gBAC5C,gCAAgC,mBAAmB;gBACnD,eAAe,mBAAmB;gBAClC,oBAAoB,mBAAmB;gBACvC,yBAAyB,mBAAmB;gBAE5C,yBAAyB;gBACzB,oBAAoB;gBACpB,wBAAwB;gBACxB,sBAAsB;gBACtB,wBAAwB;gBACxB,wBAAwB;gBACxB,YAAY;gBAEZ,0BAA0B;gBAC1B,eAAe,mBAAmB,iBAAiB,QAAQ,aAAa;gBACxE,SAAS,mBAAmB,WAAW,QAAQ,OAAO;gBACtD,OAAO,mBAAmB,SAAS,QAAQ,KAAK;YAClD;QACF;QAGF,OAAO;IACT;IAEA,MAAM,YAAW,EAAU;QACzB,mBAAmB;QACnB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,cAClD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,cAAc,MAAM;QAExB,kDAAkD;QAClD,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,cACvC,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,IACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,MAAM;QAET,iEAAiE;QACjE,MAAM,cAAc;YAClB,GAAG,OAAO;YACV,iBAAiB,mBAAmB,mBAAmB,QAAQ,eAAe;YAC9E,WAAW,mBAAmB,aAAa,QAAQ,SAAS;YAC5D,qBAAqB,mBAAmB,uBAAuB,QAAQ,mBAAmB;YAC1F,sBAAsB,mBAAmB,wBAAwB,QAAQ,oBAAoB;YAC7F,iBAAiB,mBAAmB,mBAAmB,QAAQ,eAAe;YAC9E,mBAAmB,mBAAmB,0BAA0B,QAAQ,iBAAiB;YACzF,yBAAyB,mBAAmB,2BAA2B,QAAQ,uBAAuB;YACtG,gCAAgC,mBAAmB,kCAAkC,QAAQ,8BAA8B;YAC3H,eAAe,mBAAmB,iBAAiB,QAAQ,aAAa;YACxE,oBAAoB,mBAAmB,sBAAsB,QAAQ,kBAAkB;YACvF,yBAAyB,mBAAmB,2BAA2B,QAAQ,uBAAuB;YACtG,YAAY,mBAAmB,kBAAkB,QAAQ,UAAU;QACrE;QAEA,OAAO;IACT;IAEA,MAAM,eAAc,OAAgE;QAClF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAgE;QAC9F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,wBAAuB,SAAiB;QAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,WACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,eAAe;IACf,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAK;QAE7C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,WACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,EAAU;QAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,WAAgB;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,EAAU,EAAE,OAAY;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,gBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,WAAW;IACX,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;QAEpC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;QAEpC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,EAAU;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,OAAY;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAY;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,EAAU,EAAE,OAAY;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,mBAAkB,SAAkB;QACxC,kCAAkC;QAClC,IAAI,sBAAsB,cACvB,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;MAcT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,WAAW;YACb,sBAAsB,oBAAoB,EAAE,CAAC,cAAc;QAC7D;QAEA,4CAA4C;QAC5C,IAAI,oBAAoB,cACrB,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBT,CAAC,EACA,GAAG,CAAC,mBAAmB,MAAM,MAC7B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,WAAW;YACb,oBAAoB,kBAAkB,EAAE,CAAC,WAAW;QACtD;QAEA,MAAM,CAAC,sBAAsB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnE;YACA;SACD;QAED,IAAI,qBAAqB,KAAK,EAAE,MAAM,qBAAqB,KAAK;QAChE,IAAI,mBAAmB,KAAK,EAAE,MAAM,mBAAmB,KAAK;QAE5D,8BAA8B;QAC9B,MAAM,iBAAiB,qBAAqB,IAAI,IAAI,EAAE;QACtD,MAAM,qBAAqB,CAAC,mBAAmB,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,cAAe,CAAC;gBAC7E,IAAI,YAAY,EAAE;gBAClB,YAAY,YAAY,UAAU;gBAClC,YAAY,YAAY,OAAO;gBAC/B,aAAa;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY,OAAO,EAAE;gBAChD,aAAa,CAAC,6DAA6D,EAAE,YAAY,OAAO,EAAE;gBAClG,SAAS;oBACP,SAAS,YAAY,OAAO;oBAC5B,kBAAkB,YAAY,cAAc;oBAC5C,kBAAkB,YAAY,cAAc;oBAC5C,iBAAiB,YAAY,eAAe;oBAC5C,WAAW,YAAY,SAAS;oBAChC,qBAAqB,YAAY,mBAAmB;oBACpD,sBAAsB,YAAY,oBAAoB;oBACtD,iBAAiB,YAAY,eAAe;oBAC5C,mBAAmB;wBACjB,MAAM,YAAY,sBAAsB;wBACxC,OAAO,YAAY,uBAAuB;wBAC1C,cAAc,YAAY,8BAA8B;oBAC1D;oBACA,WAAW;wBACT,eAAe,YAAY,aAAa;wBACxC,UAAU,YAAY,kBAAkB;wBACxC,eAAe,YAAY,uBAAuB;oBACpD;gBACF;gBACA,eAAe;oBACb,IAAI,YAAY,OAAO;oBACvB,WAAW,YAAY,IAAI;oBAC3B,OAAO,YAAY,KAAK;oBACxB,OAAO,YAAY,KAAK;gBAC1B;gBACA,cAAc;YAChB,CAAC;QAED,+BAA+B;QAC/B,MAAM,aAAa;eAAI;eAAmB;SAAmB,CAC1D,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAEnF,OAAO;IACT;IAEA,MAAM,kBAAiB,EAAU;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,MAAW;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,EAAU,EAAE,OAAY;QAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,qBAAqB;IACrB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK,GAChC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,wBAAuB,IAAS;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YACN,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;YAC3B,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,YAAY,IAAI;YACnC,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,sBAAsB;YACtB,OAAO,KAAK,KAAK;QACnB,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,wBAAuB,EAAU,EAAE,OAAY;QACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,wBAAuB,EAAU;QACrC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,cACrB,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,MAAM,uBAAsB,IAAY,EAAE,IAAa;QACrD,sCAAsC;QACtC,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,cAC9C,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,gBAAgB;QAEtB,IAAI,YAAY,MAAM;QAEtB,yCAAyC;QACzC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,cAC5D,IAAI,CAAC,gBACL,MAAM,CAAC,0BACP,EAAE,CAAC,kBAAkB,MACrB,EAAE,CAAC,UAAU;YAAC;YAAW;YAAa;SAAc;QAEvD,IAAI,mBAAmB,MAAM;QAE7B,IAAI,MAAM;YACR,2BAA2B;YAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA;gBAC1B,OAAO,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,QAAQ;YACzD;YAEA,IAAI,CAAC,UAAU;gBACb,OAAO;oBACL,WAAW;oBACX,QAAQ;gBACV;YACF;YAEA,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,cAAc,KAAK,MAAM,MAAM;YACzF,MAAM,YAAY,qBAAqB,SAAS,gBAAgB;YAEhE,OAAO;gBACL;gBACA,QAAQ,YAAY,OAAO;gBAC3B,iBAAiB,SAAS,gBAAgB;gBAC1C,qBAAqB;YACvB;QACF;QAEA,0CAA0C;QAC1C,MAAM,wBAAwB,MAAM,GAAG,CAAC,CAAA;YACtC,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA;gBAC7C,OAAO,IAAI,cAAc,IAAI,KAAK,UAAU,IAAI,IAAI,cAAc,IAAI,KAAK,QAAQ;YACrF,GAAG,MAAM;YAET,OAAO;gBACL,GAAG,IAAI;gBACP,sBAAsB;gBACtB,WAAW,qBAAqB,KAAK,gBAAgB;YACvD;QACF;QAEA,OAAO;YACL;YACA,OAAO;QACT;IACF;IAEA,YAAY;IACZ,MAAM;QACJ,MAAM,CAAC,gBAAgB,oBAAoB,gBAAgB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACjG,cAAc,IAAI,CAAC,iBAAiB,MAAM,CAAC,MAAM;gBAAE,OAAO;YAAQ;YAClE,cAAc,IAAI,CAAC,gBAAgB,MAAM,CAAC,cAAc;gBAAE,OAAO;YAAQ;YACzE,cAAc,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM;gBAAE,OAAO;YAAQ,GAAG,EAAE,CAAC,aAAa;YAChF,cAAc,IAAI,CAAC,iBAAiB,MAAM,CAAC,MAAM;gBAAE,OAAO;YAAQ,GAAG,EAAE,CAAC,UAAU;SACnF;QAED,MAAM,gBAAgB,eAAe,KAAK,IAAI;QAC9C,MAAM,oBAAoB,mBAAmB,KAAK,IAAI;QACtD,MAAM,gBAAgB,eAAe,KAAK,IAAI;QAC9C,MAAM,cAAc,mBAAmB,KAAK,IAAI;QAEhD,8BAA8B;QAC9B,MAAM,eAAe,mBAAmB,IAAI,IAAI,EAAE;QAClD,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QACnF,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACvF,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACvF,MAAM,yBAAyB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;QAE1F,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/app/api/availability-slots/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { supabaseAdmin } from '@/lib/supabase-server'\n\nexport async function GET() {\n  try {\n    const { data: slots, error } = await supabaseAdmin\n      .from('availability_slots')\n      .select('*')\n      .order('date', { ascending: true })\n      .order('start_time', { ascending: true })\n\n    if (error) throw error\n\n    return NextResponse.json(slots)\n  } catch (error) {\n    console.error('Error fetching availability slots:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch availability slots' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const body = await request.json()\n    \n    const { data: slot, error } = await supabaseAdmin\n      .from('availability_slots')\n      .insert({\n        date: body.date,\n        start_time: body.start_time,\n        end_time: body.end_time,\n        is_available: body.is_available ?? true,\n        max_appointments: body.max_appointments ?? 1,\n        current_appointments: 0,\n        notes: body.notes\n      })\n      .select()\n      .single()\n\n    if (error) throw error\n\n    return NextResponse.json(slot)\n  } catch (error) {\n    console.error('Error creating availability slot:', error)\n    return NextResponse.json(\n      { error: 'Failed to create availability slot' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,mJAAa,CAC/C,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK,GAChC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QAEjB,OAAO,gJAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqC,GAC9C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mJAAa,CAC9C,IAAI,CAAC,sBACL,MAAM,CAAC;YACN,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;YAC3B,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,YAAY,IAAI;YACnC,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,sBAAsB;YACtB,OAAO,KAAK,KAAK;QACnB,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,OAAO,gJAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqC,GAC9C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}