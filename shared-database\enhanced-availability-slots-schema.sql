-- =====================================================
-- ENHANCED AVAILABILITY SLOTS SCHEMA
-- =====================================================
-- This schema enhances the availability_slots table to support
-- day-of-week based recurring slots for better management

-- First, let's add new columns to support day-of-week based slots
ALTER TABLE availability_slots 
ADD COLUMN IF NOT EXISTS day_of_week INTEGER, -- 0=Sunday, 1=Monday, ..., 6=Saturday
ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE, -- Whether this is a recurring weekly slot
ADD COLUMN IF NOT EXISTS effective_from DATE DEFAULT CURRENT_DATE, -- When this slot becomes effective
ADD COLUMN IF NOT EXISTS effective_until DATE; -- When this slot expires (NULL = indefinite)

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_availability_slots_day_of_week ON availability_slots(day_of_week);
CREATE INDEX IF NOT EXISTS idx_availability_slots_recurring ON availability_slots(is_recurring);
CREATE INDEX IF NOT EXISTS idx_availability_slots_effective_dates ON availability_slots(effective_from, effective_until);

-- Add constraints
ALTER TABLE availability_slots 
ADD CONSTRAINT IF NOT EXISTS chk_day_of_week CHECK (day_of_week IS NULL OR (day_of_week >= 0 AND day_of_week <= 6));

-- Create a function to generate specific date slots from recurring templates
CREATE OR REPLACE FUNCTION generate_slots_for_date_range(
    start_date DATE,
    end_date DATE
) RETURNS TABLE (
    template_id UUID,
    generated_date DATE,
    day_of_week INTEGER,
    start_time TIME,
    end_time TIME,
    max_appointments INTEGER,
    is_available BOOLEAN,
    notes TEXT
) AS $$
DECLARE
    current_date DATE := start_date;
    slot_record RECORD;
BEGIN
    -- Loop through each date in the range
    WHILE current_date <= end_date LOOP
        -- Find recurring slots that match this day of week
        FOR slot_record IN 
            SELECT 
                id,
                day_of_week,
                start_time,
                end_time,
                max_appointments,
                is_available,
                notes
            FROM availability_slots 
            WHERE is_recurring = TRUE 
            AND day_of_week = EXTRACT(DOW FROM current_date)
            AND (effective_from IS NULL OR current_date >= effective_from)
            AND (effective_until IS NULL OR current_date <= effective_until)
            AND is_available = TRUE
        LOOP
            -- Check if a specific slot already exists for this date
            IF NOT EXISTS (
                SELECT 1 FROM availability_slots 
                WHERE date = current_date 
                AND start_time = slot_record.start_time 
                AND end_time = slot_record.end_time
                AND is_recurring = FALSE
            ) THEN
                -- Return the generated slot data
                template_id := slot_record.id;
                generated_date := current_date;
                day_of_week := slot_record.day_of_week;
                start_time := slot_record.start_time;
                end_time := slot_record.end_time;
                max_appointments := slot_record.max_appointments;
                is_available := slot_record.is_available;
                notes := slot_record.notes;
                
                RETURN NEXT;
            END IF;
        END LOOP;
        
        current_date := current_date + INTERVAL '1 day';
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get available slots for a specific date
CREATE OR REPLACE FUNCTION get_available_slots_for_date(target_date DATE)
RETURNS TABLE (
    slot_id UUID,
    slot_date DATE,
    start_time TIME,
    end_time TIME,
    max_appointments INTEGER,
    current_appointments INTEGER,
    is_available BOOLEAN,
    notes TEXT,
    is_from_template BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    -- First, get specific date slots (non-recurring)
    SELECT 
        a.id as slot_id,
        a.date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        a.current_appointments,
        a.is_available,
        a.notes,
        FALSE as is_from_template
    FROM availability_slots a
    WHERE a.date = target_date 
    AND a.is_recurring = FALSE
    AND a.is_available = TRUE
    
    UNION ALL
    
    -- Then, get recurring slots for this day of week (if no specific slot exists)
    SELECT 
        a.id as slot_id,
        target_date as slot_date,
        a.start_time,
        a.end_time,
        a.max_appointments,
        COALESCE((
            SELECT COUNT(*)::INTEGER 
            FROM appointments apt 
            WHERE apt.preferred_date = target_date 
            AND apt.preferred_time >= a.start_time 
            AND apt.preferred_time < a.end_time
            AND apt.status IN ('pending', 'confirmed', 'in_progress')
        ), 0) as current_appointments,
        a.is_available,
        a.notes,
        TRUE as is_from_template
    FROM availability_slots a
    WHERE a.is_recurring = TRUE
    AND a.day_of_week = EXTRACT(DOW FROM target_date)
    AND (a.effective_from IS NULL OR target_date >= a.effective_from)
    AND (a.effective_until IS NULL OR target_date <= a.effective_until)
    AND a.is_available = TRUE
    AND NOT EXISTS (
        -- Don't include if there's already a specific slot for this time
        SELECT 1 FROM availability_slots specific
        WHERE specific.date = target_date
        AND specific.start_time = a.start_time
        AND specific.end_time = a.end_time
        AND specific.is_recurring = FALSE
    )
    
    ORDER BY start_time;
END;
$$ LANGUAGE plpgsql;

-- Create sample recurring slots for weekdays and weekends
INSERT INTO availability_slots (
    day_of_week, 
    start_time, 
    end_time, 
    is_available, 
    max_appointments, 
    current_appointments, 
    notes, 
    is_recurring,
    effective_from
) VALUES
-- Monday to Friday morning slots
(1, '09:00:00', '12:00:00', true, 6, 0, 'Monday morning appointments', true, CURRENT_DATE),
(2, '09:00:00', '12:00:00', true, 6, 0, 'Tuesday morning appointments', true, CURRENT_DATE),
(3, '09:00:00', '12:00:00', true, 6, 0, 'Wednesday morning appointments', true, CURRENT_DATE),
(4, '09:00:00', '12:00:00', true, 6, 0, 'Thursday morning appointments', true, CURRENT_DATE),
(5, '09:00:00', '12:00:00', true, 6, 0, 'Friday morning appointments', true, CURRENT_DATE),

-- Monday to Friday afternoon slots
(1, '14:00:00', '18:00:00', true, 8, 0, 'Monday afternoon appointments', true, CURRENT_DATE),
(2, '14:00:00', '18:00:00', true, 8, 0, 'Tuesday afternoon appointments', true, CURRENT_DATE),
(3, '14:00:00', '18:00:00', true, 8, 0, 'Wednesday afternoon appointments', true, CURRENT_DATE),
(4, '14:00:00', '18:00:00', true, 8, 0, 'Thursday afternoon appointments', true, CURRENT_DATE),
(5, '14:00:00', '18:00:00', true, 8, 0, 'Friday afternoon appointments', true, CURRENT_DATE),

-- Saturday morning slot
(6, '09:00:00', '13:00:00', true, 8, 0, 'Saturday morning appointments', true, CURRENT_DATE)

ON CONFLICT DO NOTHING;

-- Update RLS policies to handle the new columns
DROP POLICY IF EXISTS "Anyone can view availability slots" ON availability_slots;
CREATE POLICY "Anyone can view availability slots" ON availability_slots
  FOR SELECT USING (is_available = true);

-- Create a view for easier querying of current availability
CREATE OR REPLACE VIEW current_availability AS
SELECT 
    id,
    CASE 
        WHEN is_recurring THEN 
            CASE day_of_week
                WHEN 0 THEN 'Sunday'
                WHEN 1 THEN 'Monday'
                WHEN 2 THEN 'Tuesday'
                WHEN 3 THEN 'Wednesday'
                WHEN 4 THEN 'Thursday'
                WHEN 5 THEN 'Friday'
                WHEN 6 THEN 'Saturday'
            END
        ELSE date::text
    END as display_date,
    day_of_week,
    start_time,
    end_time,
    max_appointments,
    current_appointments,
    is_available,
    is_recurring,
    notes,
    effective_from,
    effective_until
FROM availability_slots
WHERE is_available = true
ORDER BY 
    CASE WHEN is_recurring THEN day_of_week ELSE EXTRACT(DOW FROM date) END,
    start_time;

-- Success message
SELECT 'Enhanced availability slots schema created successfully! 
- Added day-of-week based recurring slots
- Created helper functions for slot generation
- Added sample recurring slots for weekdays and Saturday
- Created current_availability view for easier querying' as message;
